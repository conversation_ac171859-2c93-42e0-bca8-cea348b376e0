#!/usr/bin/env python3
"""
Test Face Recognition Tab First Load
Simulates exactly what happens when you click Face Recognition tab for the first time
"""

import sys
import os

def simulate_app_startup():
    """Simulate the app startup process"""
    print("🚀 Simulating App Startup...")
    
    try:
        # Import required modules (like app does)
        from ppe_detection_engine import PPEDetectionEngine
        
        # Simulate session state initialization
        print("🔧 Initializing detection engine with face recognition...")
        detection_engine = PPEDetectionEngine(enable_face_recognition=True)
        print("✅ Detection engine initialized")
        
        # Verify face engine is accessible
        print("👤 Testing face engine access...")
        face_engine = detection_engine.get_face_engine()
        
        if not face_engine:
            print("❌ Face engine is None - this would cause tab to be empty!")
            return None, None
        
        print("✅ Face engine accessible")
        return detection_engine, face_engine
        
    except Exception as e:
        print(f"❌ App startup simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def simulate_face_tab_click(detection_engine, face_engine):
    """Simulate clicking on Face Recognition tab"""
    print("\n🖱️ Simulating Face Recognition Tab Click...")
    
    try:
        # This is what the tab code does
        print("🔍 Checking detection engine...")
        if not detection_engine:
            print("❌ Detection engine is None - would show error")
            return False
        
        print("✅ Detection engine available")
        
        print("👤 Getting face engine...")
        face_engine_from_tab = detection_engine.get_face_engine()
        
        if not face_engine_from_tab:
            print("❌ Face engine is None - would show error")
            return False
        
        print("✅ Face engine available")
        
        # Test all the data the tab needs
        print("📊 Getting dataset info...")
        dataset_info = face_engine_from_tab.get_dataset_info()
        print(f"✅ Dataset info: {dataset_info}")
        
        print("🎯 Checking training status...")
        is_trained = face_engine_from_tab.is_trained
        print(f"✅ Is trained: {is_trained}")
        
        print("👥 Getting known faces...")
        known_faces = face_engine_from_tab.known_faces
        print(f"✅ Known faces: {known_faces}")
        
        print("🎚️ Getting confidence threshold...")
        threshold = face_engine_from_tab.confidence_threshold
        print(f"✅ Confidence threshold: {threshold}%")
        
        # Simulate what the tab would display
        print("\n📋 Tab Content Simulation:")
        print("="*50)
        print(f"👤 Face Recognition Management")
        print(f"📊 Dataset: {dataset_info['total_people']} people, {dataset_info['total_samples']} samples")
        print(f"🎯 Training Status: {'✅ Trained' if is_trained else '❌ Not Trained'}")
        print(f"👥 Known People: {list(known_faces.values())}")
        print(f"🎚️ Confidence Threshold: {threshold}%")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ Face tab simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the complete simulation"""
    print("🧪 Testing Face Recognition Tab First Load\n")
    
    # Step 1: Simulate app startup
    detection_engine, face_engine = simulate_app_startup()
    
    if not detection_engine or not face_engine:
        print("\n❌ FAIL: App startup failed - Face Recognition tab would be empty")
        return False
    
    # Step 2: Simulate clicking Face Recognition tab
    tab_success = simulate_face_tab_click(detection_engine, face_engine)
    
    # Summary
    print("\n" + "="*60)
    print("📋 SIMULATION SUMMARY")
    print("="*60)
    
    if tab_success:
        print("✅ SUCCESS: Face Recognition tab would load with content!")
        print("🎉 User would see:")
        print("   • Face Recognition Management header")
        print("   • Dataset information")
        print("   • Training status")
        print("   • Known people list")
        print("   • Confidence threshold slider")
        print("   • Training and management controls")
    else:
        print("❌ FAIL: Face Recognition tab would appear empty")
        print("⚠️  User would see error messages or blank content")
    
    return tab_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
