#!/usr/bin/env python3
"""
Test Face Recognition Tab Loading
Tests if the face recognition engine initializes properly and the tab content loads
"""

import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

def test_face_engine_initialization():
    """Test if face recognition engine initializes properly"""
    print("🧪 Testing Face Recognition Engine Initialization...")
    
    try:
        from face_recognition_engine import FaceRecognitionEngine
        
        # Initialize engine
        face_engine = FaceRecognitionEngine()
        print(f"✅ Face Recognition Engine initialized successfully")
        print(f"📊 Is trained: {face_engine.is_trained}")
        print(f"👥 Known faces: {face_engine.known_faces}")
        print(f"🎯 Confidence threshold: {face_engine.confidence_threshold}")
        
        # Test dataset info
        dataset_info = face_engine.get_dataset_info()
        print(f"📁 Dataset info: {dataset_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ Face Recognition Engine initialization failed: {e}")
        return False

def test_ppe_engine_initialization():
    """Test if PPE detection engine with face recognition initializes properly"""
    print("\n🧪 Testing PPE Detection Engine with Face Recognition...")
    
    try:
        from ppe_detection_engine import PPEDetectionEngine
        
        # Initialize engine with face recognition
        ppe_engine = PPEDetectionEngine(enable_face_recognition=True)
        print(f"✅ PPE Detection Engine initialized successfully")
        print(f"🔧 Face recognition enabled: {ppe_engine.is_face_recognition_enabled()}")
        
        # Test get_face_engine method
        face_engine = ppe_engine.get_face_engine()
        if face_engine:
            print(f"✅ Face engine accessible via get_face_engine()")
            print(f"📊 Face engine is trained: {face_engine.is_trained}")
            print(f"👥 Known faces: {face_engine.known_faces}")
        else:
            print(f"❌ Face engine not accessible")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ PPE Detection Engine initialization failed: {e}")
        return False

def test_model_files():
    """Test if model files exist and are accessible"""
    print("\n🧪 Testing Model Files...")
    
    model_files = [
        "face_model.pkl",
        "face_model.xml",
        "best.pt"
    ]
    
    all_exist = True
    for file_path in model_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {file_path} exists ({file_size:,} bytes)")
        else:
            print(f"❌ {file_path} not found")
            all_exist = False
    
    # Check dataset directory
    if os.path.exists("face_dataset"):
        people = [d for d in os.listdir("face_dataset") if os.path.isdir(os.path.join("face_dataset", d))]
        print(f"📁 Face dataset contains {len(people)} people: {people}")
        
        for person in people:
            person_dir = os.path.join("face_dataset", person)
            samples = [f for f in os.listdir(person_dir) if f.endswith('.jpg')]
            print(f"   👤 {person}: {len(samples)} samples")
    else:
        print(f"❌ face_dataset directory not found")
        all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("🚀 Starting Face Recognition Tab Loading Tests\n")
    
    tests = [
        ("Model Files", test_model_files),
        ("Face Engine Initialization", test_face_engine_initialization),
        ("PPE Engine Initialization", test_ppe_engine_initialization),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Face Recognition tab should load properly!")
    else:
        print("⚠️  Some tests failed. Face Recognition tab may not load correctly.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
