#!/usr/bin/env python3
"""
Test Face Recognition Functionality
Tests the complete face recognition workflow including training and recognition
"""

import os
import cv2
import numpy as np
from face_recognition_engine import FaceRecognitionEngine

def test_face_recognition_workflow():
    """Test the complete face recognition workflow"""
    print("🧪 Testing Face Recognition Workflow")
    print("=" * 50)
    
    # Initialize face recognition engine
    face_engine = FaceRecognitionEngine()
    
    # Check dataset info
    dataset_info = face_engine.get_dataset_info()
    print(f"📊 Dataset Info:")
    print(f"   - Total People: {dataset_info['total_people']}")
    print(f"   - Total Samples: {dataset_info['total_samples']}")
    
    if dataset_info['total_people'] > 0:
        print(f"   - People: {[person['name'] for person in dataset_info['people']]}")
        
        # Train the model if we have data
        print("\n🎯 Training Model...")
        training_result = face_engine.train_model()
        print(f"   - Status: {training_result['status']}")
        print(f"   - People Trained: {training_result['people_trained']}")
        print(f"   - Total Samples: {training_result['total_samples']}")
        
        if training_result['status'] == 'completed':
            print("✅ Model training completed successfully!")
            
            # Test model status
            model_status = face_engine.get_model_status()
            print(f"\n📋 Model Status:")
            print(f"   - Is Trained: {model_status['is_trained']}")
            print(f"   - People Count: {model_status['people_count']}")
            print(f"   - Known People: {model_status['known_people']}")
            print(f"   - Confidence Threshold: {model_status['confidence_threshold']}%")
            print(f"   - Model Ready: {model_status['model_ready']}")
            
            # Test face recognition on a sample image
            print(f"\n🔍 Testing Face Recognition...")
            
            # Create a test image (you can replace this with an actual image)
            test_image = np.zeros((480, 640, 3), dtype=np.uint8)
            test_image.fill(128)  # Gray background
            
            # Add some simple shapes to simulate a face
            cv2.rectangle(test_image, (250, 150), (390, 330), (200, 200, 200), -1)  # Face
            cv2.circle(test_image, (290, 200), 10, (0, 0, 0), -1)  # Left eye
            cv2.circle(test_image, (350, 200), 10, (0, 0, 0), -1)  # Right eye
            cv2.rectangle(test_image, (310, 250), (330, 270), (0, 0, 0), -1)  # Nose
            cv2.rectangle(test_image, (300, 290), (340, 310), (0, 0, 0), -1)  # Mouth
            
            # Test face detection
            face_results = face_engine.recognize_faces(test_image)
            print(f"   - Faces Detected: {len(face_results)}")
            
            for i, face in enumerate(face_results):
                print(f"   - Face {i+1}:")
                print(f"     * Recognized Person: {face['recognized_person']}")
                print(f"     * Recognition Confidence: {face['recognition_confidence']:.1f}%")
                print(f"     * Bounding Box: {face['bbox']}")
            
            return True
        else:
            print(f"❌ Model training failed: {training_result.get('error', 'Unknown error')}")
            return False
    else:
        print("⚠️ No face data found. Please collect face samples first.")
        print("   Go to the Face Recognition tab in the app and collect samples.")
        return False

def test_confidence_threshold():
    """Test confidence threshold adjustment"""
    print("\n🎛️ Testing Confidence Threshold Adjustment")
    print("-" * 40)
    
    face_engine = FaceRecognitionEngine()
    
    original_threshold = face_engine.confidence_threshold
    print(f"Original threshold: {original_threshold}%")
    
    # Test threshold updates
    test_thresholds = [50, 75, 90, 95]
    for threshold in test_thresholds:
        face_engine.update_confidence_threshold(threshold)
        print(f"Updated to {threshold}%: Current = {face_engine.confidence_threshold}%")
    
    # Reset to original
    face_engine.update_confidence_threshold(original_threshold)
    print(f"Reset to original: {face_engine.confidence_threshold}%")
    
    return True

def main():
    """Main test function"""
    print("🚀 Face Recognition Functionality Tests")
    print("=" * 60)
    
    try:
        # Test 1: Complete workflow
        workflow_success = test_face_recognition_workflow()
        
        # Test 2: Confidence threshold
        threshold_success = test_confidence_threshold()
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        if workflow_success:
            print("✅ PASS: Face Recognition Workflow")
        else:
            print("❌ FAIL: Face Recognition Workflow")
        
        if threshold_success:
            print("✅ PASS: Confidence Threshold Adjustment")
        else:
            print("❌ FAIL: Confidence Threshold Adjustment")
        
        if workflow_success and threshold_success:
            print("\n🎉 All tests passed! Face Recognition is working correctly!")
        else:
            print("\n⚠️ Some tests failed. Please check the issues above.")
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
