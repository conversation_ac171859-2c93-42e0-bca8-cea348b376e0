#!/usr/bin/env python3
"""
Test Face Recognition Tab Content
Simulates what happens when the Face Recognition tab is accessed
"""

import sys
import os

def test_face_tab_content():
    """Test if Face Recognition tab content loads properly"""
    print("🧪 Testing Face Recognition Tab Content Loading...")
    
    try:
        # Import required modules
        from ppe_detection_engine import PPEDetectionEngine
        
        # Initialize detection engine (simulating app startup)
        print("🔧 Initializing detection engine...")
        detection_engine = PPEDetectionEngine(enable_face_recognition=True)
        print("✅ Detection engine initialized")
        
        # Test get_face_engine (this is what the tab calls)
        print("👤 Getting face engine...")
        face_engine = detection_engine.get_face_engine()
        
        if face_engine is None:
            print("❌ Face engine is None - tab would be empty")
            return False
        
        print("✅ Face engine retrieved successfully")
        
        # Test face recognition status
        print("🔍 Checking face recognition status...")
        is_enabled = detection_engine.is_face_recognition_enabled()
        print(f"✅ Face recognition enabled: {is_enabled}")
        
        # Test dataset info (this is what populates the tab)
        print("📊 Getting dataset info...")
        dataset_info = face_engine.get_dataset_info()
        print(f"✅ Dataset info: {dataset_info}")
        
        # Test if model is trained
        print("🎯 Checking if model is trained...")
        is_trained = face_engine.is_trained
        print(f"✅ Model is trained: {is_trained}")
        
        # Test known faces
        print("👥 Getting known faces...")
        known_faces = face_engine.known_faces
        print(f"✅ Known faces: {known_faces}")
        
        # Test confidence threshold
        print("🎚️ Getting confidence threshold...")
        threshold = face_engine.confidence_threshold
        print(f"✅ Confidence threshold: {threshold}%")
        
        print("\n🎉 All Face Recognition tab components are accessible!")
        print("📋 Tab should display:")
        print(f"   • Dataset: {dataset_info['total_people']} people, {dataset_info['total_samples']} samples")
        print(f"   • Training Status: {'✅ Trained' if is_trained else '❌ Not Trained'}")
        print(f"   • Known People: {list(known_faces.values())}")
        print(f"   • Confidence Threshold: {threshold}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Face Recognition tab content test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the test"""
    print("🚀 Starting Face Recognition Tab Content Test\n")
    
    success = test_face_tab_content()
    
    print("\n" + "="*50)
    print("📋 TEST SUMMARY")
    print("="*50)
    
    if success:
        print("✅ PASS Face Recognition Tab Content")
        print("🎉 Face Recognition tab should load with content immediately!")
    else:
        print("❌ FAIL Face Recognition Tab Content")
        print("⚠️  Face Recognition tab may appear empty on first load.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
