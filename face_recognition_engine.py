"""
Face Recognition Engine
Integrated face recognition system for PPE monitoring with dataset management
"""

import cv2
import numpy as np
import os
import pickle
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import tempfile

class FaceRecognitionEngine:
    """Face recognition engine using OpenCV LBPH recognizer"""
    
    def __init__(self, dataset_path: str = "face_dataset", model_path: str = "face_model.yml"):
        """Initialize the face recognition engine
        
        Args:
            dataset_path: Path to store face dataset images
            model_path: Path to save/load the trained model
        """
        self.dataset_path = dataset_path
        self.model_path = model_path
        self.cascade_path = "haarcascade_frontalface_default.xml"
        
        # Create dataset directory if it doesn't exist
        os.makedirs(self.dataset_path, exist_ok=True)
        
        # Initialize face cascade classifier
        self.face_cascade = None
        self.load_cascade()
        
        # Initialize face recognizer
        self.recognizer = cv2.face.LBPHFaceRecognizer_create()
        self.is_trained = False
        self.face_labels = {}  # Maps label IDs to names
        self.label_names = {}  # Maps names to label IDs
        
        # Load existing model if available
        self.load_model()
        
        # Recognition settings
        self.confidence_threshold = 82
        self.recognition_distance_threshold = 100
        
    def load_cascade(self) -> bool:
        """Load the Haar cascade classifier for face detection
        
        Returns:
            bool: True if loaded successfully, False otherwise
        """
        try:
            # Try to load from current directory first
            if os.path.exists(self.cascade_path):
                self.face_cascade = cv2.CascadeClassifier(self.cascade_path)
            else:
                # Try to load from OpenCV data directory
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            
            if self.face_cascade.empty():
                logging.error("Failed to load face cascade classifier")
                return False
            return True
        except Exception as e:
            logging.error(f"Error loading face cascade: {e}")
            return False
    
    def extract_face(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Extract face from image
        
        Args:
            image: Input image as numpy array
            
        Returns:
            Cropped face image or None if no face found
        """
        if self.face_cascade is None:
            return None
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5)
        
        if len(faces) == 0:
            return None
            
        # Return the first (largest) face found
        x, y, w, h = faces[0]
        return image[y:y+h, x:x+w]
    
    def detect_faces(self, image: np.ndarray) -> List[Dict]:
        """Detect all faces in image with bounding boxes
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of face detection dictionaries with bbox and face image
        """
        if self.face_cascade is None:
            return []
            
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5)
        
        face_detections = []
        for (x, y, w, h) in faces:
            face_img = image[y:y+h, x:x+w]
            face_detections.append({
                'bbox': [x, y, x+w, y+h],
                'face_image': face_img,
                'center': (x + w//2, y + h//2)
            })
            
        return face_detections
    
    def collect_face_samples(self, person_name: str, num_samples: int = 100) -> Dict:
        """Collect face samples for a person using webcam
        
        Args:
            person_name: Name of the person
            num_samples: Number of samples to collect
            
        Returns:
            Dictionary with collection results
        """
        if not person_name.strip():
            return {"error": "Person name cannot be empty"}
            
        # Create person directory
        person_dir = os.path.join(self.dataset_path, person_name)
        os.makedirs(person_dir, exist_ok=True)
        
        cap = cv2.VideoCapture(0)
        count = 0
        collected_samples = []
        
        try:
            while count < num_samples:
                ret, frame = cap.read()
                if not ret:
                    break
                    
                face = self.extract_face(frame)
                if face is not None:
                    count += 1
                    # Resize and convert to grayscale
                    face_resized = cv2.resize(face, (200, 200))
                    face_gray = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)
                    
                    # Save the sample
                    filename = f"{person_name}_{count}.jpg"
                    filepath = os.path.join(person_dir, filename)
                    cv2.imwrite(filepath, face_gray)
                    collected_samples.append(filepath)
                    
                    # Display progress
                    cv2.putText(face_resized, f"{count}/{num_samples}", (10, 30), 
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    cv2.imshow('Collecting Face Samples', face_resized)
                    
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
                    
        finally:
            cap.release()
            cv2.destroyAllWindows()
            
        return {
            "person_name": person_name,
            "samples_collected": count,
            "samples_paths": collected_samples,
            "success": count > 0
        }

    def train_model(self) -> Dict:
        """Train the face recognition model with collected samples

        Returns:
            Dictionary with training results
        """
        try:
            training_data = []
            labels = []
            current_label = 0
            self.face_labels = {}
            self.label_names = {}

            # Scan dataset directory for person folders
            if not os.path.exists(self.dataset_path):
                return {"error": "Dataset path does not exist"}

            person_folders = [f for f in os.listdir(self.dataset_path)
                            if os.path.isdir(os.path.join(self.dataset_path, f))]

            if not person_folders:
                return {"error": "No person folders found in dataset"}

            total_samples = 0
            for person_name in person_folders:
                person_dir = os.path.join(self.dataset_path, person_name)
                image_files = [f for f in os.listdir(person_dir)
                             if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

                if not image_files:
                    continue

                # Assign label to this person
                self.face_labels[current_label] = person_name
                self.label_names[person_name] = current_label

                # Load images for this person
                for image_file in image_files:
                    image_path = os.path.join(person_dir, image_file)
                    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)

                    if image is not None:
                        # Resize to standard size
                        image = cv2.resize(image, (200, 200))
                        training_data.append(image)
                        labels.append(current_label)
                        total_samples += 1

                current_label += 1

            if not training_data:
                return {"error": "No valid training images found"}

            # Train the recognizer
            training_data = np.array(training_data)
            labels = np.array(labels, dtype=np.int32)

            self.recognizer.train(training_data, labels)
            self.is_trained = True

            # Save the model
            self.save_model()

            return {
                "success": True,
                "total_samples": total_samples,
                "num_people": len(person_folders),
                "people_names": list(self.label_names.keys())
            }

        except Exception as e:
            logging.error(f"Error training model: {e}")
            return {"error": f"Training failed: {str(e)}"}

    def save_model(self) -> bool:
        """Save the trained model and labels

        Returns:
            bool: True if saved successfully
        """
        try:
            if self.is_trained:
                # Save the recognizer model
                self.recognizer.save(self.model_path)

                # Save the labels mapping
                labels_path = self.model_path.replace('.yml', '_labels.pkl')
                with open(labels_path, 'wb') as f:
                    pickle.dump({
                        'face_labels': self.face_labels,
                        'label_names': self.label_names
                    }, f)

                return True
        except Exception as e:
            logging.error(f"Error saving model: {e}")
        return False

    def load_model(self) -> bool:
        """Load the trained model and labels

        Returns:
            bool: True if loaded successfully
        """
        try:
            if os.path.exists(self.model_path):
                # Load the recognizer model
                self.recognizer.read(self.model_path)
                self.is_trained = True

                # Load the labels mapping
                labels_path = self.model_path.replace('.yml', '_labels.pkl')
                if os.path.exists(labels_path):
                    with open(labels_path, 'rb') as f:
                        labels_data = pickle.load(f)
                        self.face_labels = labels_data.get('face_labels', {})
                        self.label_names = labels_data.get('label_names', {})

                return True
        except Exception as e:
            logging.error(f"Error loading model: {e}")
        return False

    def recognize_faces(self, image: np.ndarray) -> List[Dict]:
        """Recognize faces in an image

        Args:
            image: Input image as numpy array

        Returns:
            List of recognition results with bbox, name, and confidence
        """
        if not self.is_trained or self.face_cascade is None:
            return []

        face_detections = self.detect_faces(image)
        recognition_results = []

        for detection in face_detections:
            face_img = detection['face_image']

            # Resize and convert to grayscale for recognition
            face_resized = cv2.resize(face_img, (200, 200))
            face_gray = cv2.cvtColor(face_resized, cv2.COLOR_BGR2GRAY)

            # Perform recognition
            label, distance = self.recognizer.predict(face_gray)

            # Calculate confidence
            confidence = 0
            if distance < self.recognition_distance_threshold:
                confidence = int(100 * (1 - distance / 300))

            # Determine recognition result
            person_name = "Unknown"
            is_recognized = False

            if confidence > self.confidence_threshold and label in self.face_labels:
                person_name = self.face_labels[label]
                is_recognized = True

            recognition_results.append({
                'bbox': detection['bbox'],
                'person_name': person_name,
                'confidence': confidence,
                'is_recognized': is_recognized,
                'distance': distance,
                'center': detection['center']
            })

        return recognition_results

    def draw_face_recognition(self, image: np.ndarray, recognition_results: List[Dict]) -> np.ndarray:
        """Draw face recognition results on image

        Args:
            image: Input image
            recognition_results: List of recognition result dictionaries

        Returns:
            Image with drawn face recognition results
        """
        result_image = image.copy()

        for result in recognition_results:
            bbox = result['bbox']
            person_name = result['person_name']
            confidence = result['confidence']
            is_recognized = result['is_recognized']

            x1, y1, x2, y2 = bbox

            # Choose color based on recognition status
            if is_recognized:
                color = (0, 255, 0)  # Green for recognized
                text = f"{person_name} ({confidence}%)"
            else:
                color = (0, 0, 255)  # Red for unknown
                text = "Unknown"

            # Draw bounding box
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)

            # Draw label background
            label_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(result_image, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), color, -1)

            # Draw label text
            cv2.putText(result_image, text, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return result_image

    def get_dataset_info(self) -> Dict:
        """Get information about the current dataset

        Returns:
            Dictionary with dataset information
        """
        info = {
            "dataset_path": self.dataset_path,
            "model_path": self.model_path,
            "is_trained": self.is_trained,
            "people": [],
            "total_samples": 0
        }

        if os.path.exists(self.dataset_path):
            person_folders = [f for f in os.listdir(self.dataset_path)
                            if os.path.isdir(os.path.join(self.dataset_path, f))]

            for person_name in person_folders:
                person_dir = os.path.join(self.dataset_path, person_name)
                image_files = [f for f in os.listdir(person_dir)
                             if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

                person_info = {
                    "name": person_name,
                    "samples": len(image_files),
                    "folder_path": person_dir
                }
                info["people"].append(person_info)
                info["total_samples"] += len(image_files)

        return info

    def delete_person_data(self, person_name: str) -> bool:
        """Delete all data for a specific person

        Args:
            person_name: Name of the person to delete

        Returns:
            bool: True if deleted successfully
        """
        try:
            person_dir = os.path.join(self.dataset_path, person_name)
            if os.path.exists(person_dir):
                import shutil
                shutil.rmtree(person_dir)
                return True
        except Exception as e:
            logging.error(f"Error deleting person data: {e}")
        return False
