#!/usr/bin/env python3
"""
Simple test to verify Face Recognition tab content
"""

def test_face_recognition_components():
    """Test all components that should appear in Face Recognition tab"""
    print("🧪 Testing Face Recognition Tab Components...")
    
    try:
        # Test 1: Import modules
        print("📦 Testing imports...")
        from ppe_detection_engine import PPEDetectionEngine
        print("✅ PPEDetectionEngine imported")
        
        # Test 2: Initialize detection engine
        print("🔧 Testing detection engine initialization...")
        detection_engine = PPEDetectionEngine(enable_face_recognition=True)
        print("✅ Detection engine initialized")
        
        # Test 3: Get face engine
        print("👤 Testing face engine access...")
        face_engine = detection_engine.get_face_engine()
        if face_engine:
            print("✅ Face engine accessible")
        else:
            print("❌ Face engine is None")
            return False
        
        # Test 4: Get dataset info
        print("📊 Testing dataset info...")
        dataset_info = face_engine.get_dataset_info()
        print(f"✅ Dataset info: {dataset_info}")
        
        # Test 5: Check training status
        print("🎯 Testing training status...")
        is_trained = face_engine.is_trained
        print(f"✅ Is trained: {is_trained}")
        
        # Test 6: Get confidence threshold
        print("🎚️ Testing confidence threshold...")
        threshold = face_engine.confidence_threshold
        print(f"✅ Confidence threshold: {threshold}%")
        
        # Test 7: Test threshold update
        print("🔄 Testing threshold update...")
        face_engine.update_confidence_threshold(65)
        new_threshold = face_engine.confidence_threshold
        print(f"✅ Threshold updated to: {new_threshold}%")
        
        print("\n🎉 All components working correctly!")
        print("📋 Face Recognition tab should show:")
        print(f"   • Title: 👤 Face Recognition Management")
        print(f"   • Dataset: {dataset_info['total_people']} people, {dataset_info['total_samples']} samples")
        print(f"   • Training: {'✅ Trained' if is_trained else '❌ Not Trained'}")
        print(f"   • People: {[p['name'] for p in dataset_info['people']]}")
        print(f"   • Threshold: {new_threshold}% (with slider)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_face_recognition_components()
    if success:
        print("\n✅ SUCCESS: Face Recognition tab should work properly!")
    else:
        print("\n❌ FAILED: Face Recognition tab may have issues")
